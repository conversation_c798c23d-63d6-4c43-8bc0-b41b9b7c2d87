import { Component, Environment, Project, ProjectWithContributors } from '../../types/choreo';

export interface ChoreoApiService {
  getComponents(orgHandler?: string, projectId?: string): Promise<Component[]>;
  getEnvironments(orgUuid?: string, projectId?: string, type?: string): Promise<Environment[]>;
  getProject(orgId?: number, projectId?: string): Promise<Project>;
  getProjectContributors(orgId?: number, projectId?: string): Promise<ProjectWithContributors>;
  healthCheck(): Promise<boolean>;
}
