import {
  mockCredentials,
  mockErrorHandler,
  mockServices,
} from '@backstage/backend-test-utils';
import express from 'express';
import request from 'supertest';

import { createRouter } from './router';
import { TodoListService } from './services/TodoListService/types';
import { ChoreoApiService } from './services/ChoreoApiService';

const mockTodoItem = {
  title: 'Do the thing',
  id: '123',
  createdBy: mockCredentials.user().principal.userEntityRef,
  createdAt: new Date().toISOString(),
};

// TEMPLATE NOTE:
// Testing the router directly allows you to write a unit test that mocks the provided options.
describe('createRouter', () => {
  let app: express.Express;
  let todoListService: jest.Mocked<TodoListService>;
  let choreoApiService: jest.Mocked<ChoreoApiService>;

  beforeEach(async () => {
    todoListService = {
      createTodo: jest.fn(),
      listTodos: jest.fn(),
      getTodo: jest.fn(),
    };
    choreoApiService = {
      getComponents: jest.fn(),
      getEnvironments: jest.fn(),
      getProject: jest.fn(),
      getProjectContributors: jest.fn(),
      healthCheck: jest.fn(),
    };
    const router = await createRouter({
      httpAuth: mockServices.httpAuth(),
      todoListService,
      choreoApiService,
    });
    app = express();
    app.use(router);
    app.use(mockErrorHandler());
  });

  it('should create a TODO', async () => {
    todoListService.createTodo.mockResolvedValue(mockTodoItem);

    const response = await request(app).post('/todos').send({
      title: 'Do the thing',
    });

    expect(response.status).toBe(201);
    expect(response.body).toEqual(mockTodoItem);
  });

  it('should not allow unauthenticated requests to create a TODO', async () => {
    todoListService.createTodo.mockResolvedValue(mockTodoItem);

    // TEMPLATE NOTE:
    // The HttpAuth mock service considers all requests to be authenticated as a
    // mock user by default. In order to test other cases we need to explicitly
    // pass an authorization header with mock credentials.
    const response = await request(app)
      .post('/todos')
      .set('Authorization', mockCredentials.none.header())
      .send({
        title: 'Do the thing',
      });

    expect(response.status).toBe(401);
  });

  describe('Choreo API endpoints', () => {
    it('should get components', async () => {
      const mockComponents = {
        data: {
          components: [
            {
              id: 'test-component',
              name: 'Test Component',
              description: 'A test component',
            },
          ],
        },
      };
      choreoApiService.getComponents.mockResolvedValue(mockComponents);

      const response = await request(app).get('/choreo/components');

      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockComponents);
    });

    it('should get environments', async () => {
      const mockEnvironments = {
        data: {
          environments: [
            {
              id: 'test-env',
              name: 'Test Environment',
              type: 'Development',
            },
          ],
        },
      };
      choreoApiService.getEnvironments.mockResolvedValue(mockEnvironments);

      const response = await request(app).get('/choreo/environments');

      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockEnvironments);
    });

    it('should handle API errors gracefully', async () => {
      choreoApiService.getComponents.mockRejectedValue(new Error('API Error'));

      const response = await request(app).get('/choreo/components');

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        error: 'Failed to fetch components',
        message: 'API Error',
      });
    });

    it('should check health', async () => {
      choreoApiService.healthCheck.mockResolvedValue(true);

      const response = await request(app).get('/choreo/health');

      expect(response.status).toBe(200);
      expect(response.body).toEqual({ healthy: true });
    });
  });
});
