/**
 * Choreo API Types
 * TypeScript interfaces for Choreo GraphQL API responses
 */

// Organization types
export interface Organization {
  handle: string;
  uuid: string;
  name?: string;
}

// Project types
export interface Project {
  id: string;
  orgId: number;
  createdDate: string;
  name: string;
  handler: string;
  extendedHandler: string;
  description: string;
  owner: string;
  version: string;
  labels: string[];
  region: string;
  secretRef: string;
  allowedPermissions: Array<{
    key: string;
    value: string;
  }>;
  defaultDeploymentPipelineId: string;
  deploymentPipelineIds: string[];
  type: string;
  gitProvider: string;
  gitOrganization: string;
  repository: string;
  branch: string;
  updatedAt: string;
}

// Project Contributors types
export interface ProjectContributor {
  id: string;
  pictureUrl: string;
  email: string;
  displayName: string;
  totalContributions: number;
}

export interface ProjectContributorsData {
  contributorCount: number;
  contributors: ProjectContributor[];
}

export interface ProjectWithContributors {
  projectContributorsData: ProjectContributorsData;
}

// Component types
export interface BuildpackConfig {
  versionId: string;
  buildContext: string;
  languageVersion: string;
  buildpack: {
    id: string;
    language: string;
  };
}

export interface ByocWebAppBuildConfig {
  id: string;
  dockerContext: string;
  webAppType: string;
}

export interface Repository {
  buildpackConfig?: BuildpackConfig;
  byocWebAppBuildConfig?: ByocWebAppBuildConfig;
}

export interface ApiVersion {
  apiVersion: string;
  proxyName: string;
  proxyUrl: string;
  proxyId: string;
  id: string;
  state: string;
  latest: boolean;
  branch: string;
  accessibility: string;
}

export interface DeploymentTrack {
  id: string;
  createdAt: string;
  updatedAt: string;
  apiVersion: string;
  branch: string;
  description: string;
  componentId: string;
  latest: boolean;
  versionStrategy: string;
  autoDeployEnabled: boolean;
}

export interface Component {
  projectId: string;
  id: string;
  description: string;
  status: string;
  initStatus: string;
  name: string;
  handler: string;
  displayName: string;
  displayType: string;
  version: string;
  createdAt: string;
  lastBuildDate: string;
  orgHandler: string;
  isSystemComponent: boolean;
  repository: Repository;
  componentSubType: string;
  apiVersions: ApiVersion[];
  deploymentTracks: DeploymentTrack[];
}

// Environment types
export interface Environment {
  name: string;
  id: string;
  choreoEnv: string;
  vhost: string;
  apiEnvName: string;
  isMigrating: boolean;
  apimEnvId: string;
  namespace: string;
  sandboxVhost: string;
  critical: boolean;
  isPdp: boolean;
  promoteFrom: string;
  dpId: string;
  templateId: string;
  scaleToZeroEnabled: boolean;
}

// GraphQL Query Variables
export interface ComponentsQueryVariables {
  orgHandler: string;
  projectId: string;
}

export interface EnvironmentsQueryVariables {
  orgUuid: string;
  type: string;
  projectId: string;
}

export interface ProjectQueryVariables {
  orgId: number;
  projectId: string;
}

// API Response types
export interface ComponentsResponse {
  components: Component[];
}

export interface EnvironmentsResponse {
  environments: Environment[];
}

export interface ProjectResponse {
  project: Project;
}

export interface ProjectContributorsResponse {
  project: ProjectWithContributors;
}

// Configuration types
export interface ChoreoConfig {
  apiUrl: string;
  auth: {
    token: string;
  };
  defaultOrg: {
    handler: string;
    uuid: string;
    id: number;
  };
  defaultProject: {
    id: string;
  };
}
