import { LoggerService } from '@backstage/backend-plugin-api';
import { GraphQLClient } from 'graphql-request';
import {
  ChoreoConfig,
  Component,
  Environment,
  Project,
  ProjectWithContributors,
  ComponentsQueryVariables,
  EnvironmentsQueryVariables,
  ProjectQueryVariables,
  ComponentsResponse,
  EnvironmentsResponse,
  ProjectResponse,
  ProjectContributorsResponse,
} from '../../types/choreo';
import {
  COMPONENTS_QUERY,
  ENVIRONMENTS_QUERY,
  PROJECT_QUERY,
  PROJECT_CONTRIBUTORS_QUERY,
} from '../../queries/choreo';

export interface ChoreoApiServiceOptions {
  logger: LoggerService;
  config: ChoreoConfig;
}

export class ChoreoApiService {
  private readonly logger: LoggerService;
  private readonly client: GraphQLClient;
  private readonly config: ChoreoConfig;

  constructor(options: ChoreoApiServiceOptions) {
    this.logger = options.logger;
    this.config = options.config;
    
    this.client = new GraphQLClient(this.config.apiUrl, {
      headers: {
        'Authorization': `Bearer ${this.config.auth.token}`,
        'Content-Type': 'application/json',
      },
    });

    this.logger.info('ChoreoApiService initialized');
  }

  async getComponents(
    orgHandler?: string,
    projectId?: string,
  ): Promise<Component[]> {
    try {
      const variables: ComponentsQueryVariables = {
        orgHandler: orgHandler || this.config.defaultOrg.handler,
        projectId: projectId || this.config.defaultProject.id,
      };

      this.logger.debug('Fetching components', { variables });

      const response = await this.client.request<ComponentsResponse>(
        COMPONENTS_QUERY,
        variables,
      );

      this.logger.info(`Fetched ${response.components.length} components`);
      return response.components;
    } catch (error) {
      this.logger.error('Failed to fetch components', error);
      throw new Error(`Failed to fetch components: ${error}`);
    }
  }

  async getEnvironments(
    orgUuid?: string,
    projectId?: string,
    type: string = 'external',
  ): Promise<Environment[]> {
    try {
      const variables: EnvironmentsQueryVariables = {
        orgUuid: orgUuid || this.config.defaultOrg.uuid,
        type,
        projectId: projectId || this.config.defaultProject.id,
      };

      this.logger.debug('Fetching environments', { variables });

      const response = await this.client.request<EnvironmentsResponse>(
        ENVIRONMENTS_QUERY,
        variables,
      );

      this.logger.info(`Fetched ${response.environments.length} environments`);
      return response.environments;
    } catch (error) {
      this.logger.error('Failed to fetch environments', error);
      throw new Error(`Failed to fetch environments: ${error}`);
    }
  }

  async getProject(
    orgId?: number,
    projectId?: string,
  ): Promise<Project> {
    try {
      const variables: ProjectQueryVariables = {
        orgId: orgId || this.config.defaultOrg.id,
        projectId: projectId || this.config.defaultProject.id,
      };

      this.logger.debug('Fetching project', { variables });

      const response = await this.client.request<ProjectResponse>(
        PROJECT_QUERY,
        variables,
      );

      this.logger.info(`Fetched project: ${response.project.name}`);
      return response.project;
    } catch (error) {
      this.logger.error('Failed to fetch project', error);
      throw new Error(`Failed to fetch project: ${error}`);
    }
  }

  async getProjectContributors(
    orgId?: number,
    projectId?: string,
  ): Promise<ProjectWithContributors> {
    try {
      const variables: ProjectQueryVariables = {
        orgId: orgId || this.config.defaultOrg.id,
        projectId: projectId || this.config.defaultProject.id,
      };

      this.logger.debug('Fetching project contributors', { variables });

      const response = await this.client.request<ProjectContributorsResponse>(
        PROJECT_CONTRIBUTORS_QUERY,
        variables,
      );

      this.logger.info(
        `Fetched ${response.project.projectContributorsData.contributorCount} contributors`,
      );
      return response.project;
    } catch (error) {
      this.logger.error('Failed to fetch project contributors', error);
      throw new Error(`Failed to fetch project contributors: ${error}`);
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Simple health check by fetching project info
      await this.getProject();
      return true;
    } catch (error) {
      this.logger.warn('Choreo API health check failed', error);
      return false;
    }
  }
}
