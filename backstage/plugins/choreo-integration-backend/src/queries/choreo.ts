/**
 * Choreo GraphQL Queries
 */

export const COMPONENTS_QUERY = `
  query GetComponents($orgHandler: String!, $projectId: String!) {
    components(orgHandler: $orgHandler, projectId: $projectId) {
      projectId
      id
      description
      status
      initStatus
      name
      handler
      displayName
      displayType
      version
      createdAt
      lastBuildDate
      orgHandler
      isSystemComponent
      repository {
        buildpackConfig {
          versionId
          buildContext
          languageVersion
          buildpack {
            id
            language
          }
        }
        byocWebAppBuildConfig {
          id
          dockerContext
          webAppType
        }
      }
      componentSubType
      apiVersions {
        apiVersion
        proxyName
        proxyUrl
        proxyId
        id
        state
        latest
        branch
        accessibility
      }
      deploymentTracks {
        id
        createdAt
        updatedAt
        apiVersion
        branch
        description
        componentId
        latest
        versionStrategy
        autoDeployEnabled
      }
    }
  }
`;

export const ENVIRONMENTS_QUERY = `
  query GetEnvironments($orgUuid: String!, $type: String!, $projectId: String!) {
    environments(orgUuid: $orgUuid, type: $type, projectId: $projectId) {
      name
      id
      choreoEnv
      vhost
      apiEnvName
      isMigrating
      apimEnvId
      namespace
      sandboxVhost
      critical
      isPdp
      promoteFrom
      dpId
      templateId
      scaleToZeroEnabled
    }
  }
`;

export const PROJECT_QUERY = `
  query GetProject($orgId: Int!, $projectId: String!) {
    project(orgId: $orgId, projectId: $projectId) {
      id
      orgId
      createdDate
      name
      handler
      extendedHandler
      description
      owner
      version
      labels
      region
      secretRef
      allowedPermissions {
        key
        value
      }
      defaultDeploymentPipelineId
      deploymentPipelineIds
      type
      gitProvider
      gitOrganization
      repository
      branch
      updatedAt
    }
  }
`;

export const PROJECT_CONTRIBUTORS_QUERY = `
  query GetProjectContributors($orgId: Int!, $projectId: String!) {
    project(orgId: $orgId, projectId: $projectId) {
      projectContributorsData {
        contributorCount
        contributors {
          id
          pictureUrl
          email
          displayName
          totalContributions
        }
      }
    }
  }
`;
