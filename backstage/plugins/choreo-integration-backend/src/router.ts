import { HttpAuthService } from '@backstage/backend-plugin-api';
import { InputError } from '@backstage/errors';
import { z } from 'zod';
import express from 'express';
import Router from 'express-promise-router';
import { TodoListService } from './services/TodoListService/types';
import { ChoreoApiService } from './services/ChoreoApiService';

export async function createRouter({
  httpAuth,
  todoListService,
  choreoApiService,
}: {
  httpAuth: HttpAuthService;
  todoListService: TodoListService;
  choreoApiService: ChoreoApiService;
}): Promise<express.Router> {
  const router = Router();
  router.use(express.json());

  // TEMPLATE NOTE:
  // Zod is a powerful library for data validation and recommended in particular
  // for user-defined schemas. In this case we use it for input validation too.
  //
  // If you want to define a schema for your API we recommend using Backstage's
  // OpenAPI tooling: https://backstage.io/docs/next/openapi/01-getting-started
  const todoSchema = z.object({
    title: z.string(),
    entityRef: z.string().optional(),
  });

  router.post('/todos', async (req, res) => {
    const parsed = todoSchema.safeParse(req.body);
    if (!parsed.success) {
      throw new InputError(parsed.error.toString());
    }

    const result = await todoListService.createTodo(parsed.data, {
      credentials: await httpAuth.credentials(req, { allow: ['user'] }),
    });

    res.status(201).json(result);
  });

  router.get('/todos', async (_req, res) => {
    res.json(await todoListService.listTodos());
  });

  router.get('/todos/:id', async (req, res) => {
    res.json(await todoListService.getTodo({ id: req.params.id }));
  });

  // Choreo API endpoints
  router.get('/choreo/components', async (_req, res) => {
    try {
      const components = await choreoApiService.getComponents();
      res.json(components);
    } catch (error) {
      res.status(500).json({
        error: 'Failed to fetch components',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  router.get('/choreo/environments', async (_req, res) => {
    try {
      const environments = await choreoApiService.getEnvironments();
      res.json(environments);
    } catch (error) {
      res.status(500).json({
        error: 'Failed to fetch environments',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  router.get('/choreo/project', async (_req, res) => {
    try {
      const project = await choreoApiService.getProject();
      res.json(project);
    } catch (error) {
      res.status(500).json({
        error: 'Failed to fetch project',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  router.get('/choreo/project/contributors', async (_req, res) => {
    try {
      const contributors = await choreoApiService.getProjectContributors();
      res.json(contributors);
    } catch (error) {
      res.status(500).json({
        error: 'Failed to fetch project contributors',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  router.get('/choreo/health', async (_req, res) => {
    try {
      const isHealthy = await choreoApiService.healthCheck();
      res.json({ healthy: isHealthy });
    } catch (error) {
      res.status(500).json({
        healthy: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  return router;
}
