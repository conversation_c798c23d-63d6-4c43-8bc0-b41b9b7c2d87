import { LoggerService, ConfigService } from '@backstage/backend-plugin-api';
import { ChoreoApiService as ChoreoApiServiceImpl } from './ChoreoApiService';
import { ChoreoApiService } from './types';
import { ChoreoConfig } from '../../types/choreo';

export interface CreateChoreoApiServiceOptions {
  logger: LoggerService;
  config: ConfigService;
}

export async function createChoreoApiService(
  options: CreateChoreoApiServiceOptions,
): Promise<ChoreoApiService> {
  const { logger, config } = options;

  // Read configuration
  const choreoConfig: ChoreoConfig = {
    apiUrl: config.getString('choreo.apiUrl'),
    auth: {
      token: config.getString('choreo.auth.token'),
    },
    defaultOrg: {
      handler: config.getString('choreo.defaultOrg.handler'),
      uuid: config.getString('choreo.defaultOrg.uuid'),
      id: config.getNumber('choreo.defaultOrg.id'),
    },
    defaultProject: {
      id: config.getString('choreo.defaultProject.id'),
    },
  };

  logger.info('Creating ChoreoApiService with config', {
    apiUrl: choreoConfig.apiUrl,
    orgHandler: choreoConfig.defaultOrg.handler,
    projectId: choreoConfig.defaultProject.id,
  });

  return new ChoreoApiServiceImpl({
    logger,
    config: choreoConfig,
  });
}

export { ChoreoApiService } from './types';
export { ChoreoApiService as ChoreoApiServiceImpl } from './ChoreoApiService';
